"""
HTTP route decorator and core routing components
"""

# Import core components
from .metadata import RouteType, HttpMethod, RouteMetadata, create_route_metadata
from .decorators import route, systemRoute
from .registry import (
    SystemRouteRegistry,
    get_system_route_registry,
    get_database_route_registry,
    get_all_database_routes,
    register_route_for_database
)
from .fastapi_integration import setup_http_routes, RouteIntegration
from .request_processor import RequestProcessor, create_request_context
from .response_processor import ResponseProcessor, FastAPIResponseProcessor
from .auth import AuthType
from ..logging import get_logger

logger = get_logger(__name__)
